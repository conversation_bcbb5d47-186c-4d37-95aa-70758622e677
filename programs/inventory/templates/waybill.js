import path from 'path';
import fs from 'fs-extra';
import _ from 'lodash';

export default {
    name: 'waybill',
    title: 'Waybill',
    contentType: 'html',
    outputType: 'pdf',
    async getLocale(app, recordId) {
        if (!!recordId) {
            const document = await app.collection('inventory.transfers').findOne({
                _id: recordId,
                $select: ['partnerId'],
                $disableSoftDelete: true
            });

            if (!!document) {
                if (document.partnerId) {
                    const partner = await app.collection('kernel.partners').findOne({
                        _id: document.partnerId,
                        $select: ['languageId'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    if (!!partner) {
                        const language = await app.collection('kernel.languages').findOne({
                            _id: partner.languageId,
                            $select: ['isoCode'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });

                        if (!!language) {
                            return language.isoCode;
                        }
                    }
                }
            }
        }

        const company = await app.collection('kernel.company').findOne({});

        return company.language.isoCode;
    },
    async qr(app, recordId = '', data = {}) {
        return {
            text: `inventory.operations.transfers.detail/${recordId}`
        };
    },
    async record(app, recordId, params) {
        const company = await app.collection('kernel.company').findOne({});
        const d = await app.collection('inventory.transfers').findOne({
            _id: recordId,
            $populate: [
                {
                    field: 'partner',
                    query: {
                        $select: [
                            'code',
                            'name',
                            'isCompany',
                            'legalName',
                            'email',
                            'languageId',
                            'currencyId',
                            'website',
                            'identity',
                            'tin',
                            'taxDepartment',
                            'phone',
                            'phoneNumbers',
                            'address'
                        ]
                    }
                },
                {
                    field: 'branch',
                    query: {
                        $select: ['code', 'name', 'address']
                    }
                },
                {field: 'sourceLocation'},
                {field: 'destinationLocation'}
            ],
            $disableSoftDelete: true
        });
        const {locale} = params;
        const t = (text, data = {}) => app.translate(text, locale, data);
        const statusOptions = [
            {value: 'draft', label: 'Draft', color: 'default'},
            {value: 'waiting', label: 'Waiting', color: 'warning'},
            {value: 'ready', label: 'Ready', color: 'success'},
            {value: 'approved', label: 'Approved', color: 'primary'},
            {value: 'canceled', label: 'Canceled', color: 'danger'}
        ];
        const document = {};

        // Format.
        const currency = await app.collection('kernel.currencies').findOne({
            _id: company.currencyId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const formatOptions = {
            currency: {
                symbol: currency.symbol,
                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
            },
            useOutputPrecision: true
        };
        const f = (amount, type) => app.format(amount, type, formatOptions);

        document.locale = locale;
        document.status = t(statusOptions.find(o => o.value === d.status).label);
        document.code = d.code;
        document.reference = d.reference;
        document.currency = currency.name;
        document.currencyRate = f(d.currencyRate, 'decimal');
        document.recordDate = f(d.recordDate, 'datetime');
        document.issueDate = f(d.issueDate, 'date');
        document.scheduledDate = f(d.scheduledDate, 'date');
        document.scheduledTime = f(d.scheduledDate, 'time').split(':').slice(0, 2).join(':');
        document.note = (d.note || '').split('\r\n').join('<br/>').split('\n').join('<br/>');
        document.partnerOrderReference = d.partnerOrderReference;
        document.documentNo = d.documentNo;

        if (!!d.partner) {
            // Partner.
            const partner = {};
            partner.code = d.partner.code;
            partner.isCompany = d.partner.isCompany;
            partner.name = d.partner.name;
            partner.legalName = d.partner.legalName;
            partner.email = d.partner.email;
            partner.language = (
                (await app.collection('kernel.languages').findOne({
                    _id: d.partner.languageId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            partner.currency = (
                (await app.collection('kernel.currencies').findOne({
                    _id: d.partner.currencyId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            partner.website = d.partner.website;
            partner.identity = d.partner.identity;
            partner.tin = d.partner.tin;
            partner.taxDepartment = d.partner.taxDepartment;
            partner.phone = d.partner.phone;
            partner.address = d.partner.address.address;
            partner.country = (
                (await app.collection('kernel.countries').findOne({
                    _id: d.partner.address.countryId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            partner.city = d.partner.address.city;
            partner.district = d.partner.address.district;
            partner.subDistrict = d.partner.address.subDistrict;
            partner.street = d.partner.address.street;
            partner.doorNumber = d.partner.address.doorNumber;
            partner.apartmentNumber = d.partner.address.apartmentNumber;
            partner.postalCode = d.partner.address.postalCode;
            document.partner = partner;
        }

        // Delivery address.
        const deliveryAddress = {};
        if (!!d.deliveryAddress.countryId) {
            deliveryAddress.country = (
                (await app.collection('kernel.countries').findOne({
                    _id: d.deliveryAddress.countryId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
        } else {
            deliveryAddress.country = '';
        }
        deliveryAddress.address = d.deliveryAddress.address;
        deliveryAddress.city = d.deliveryAddress.city;
        deliveryAddress.district = d.deliveryAddress.district;
        deliveryAddress.subDistrict = d.deliveryAddress.subDistrict;
        deliveryAddress.street = d.deliveryAddress.street;
        deliveryAddress.doorNumber = d.deliveryAddress.doorNumber;
        deliveryAddress.apartmentNumber = d.deliveryAddress.apartmentNumber;
        deliveryAddress.postalCode = d.deliveryAddress.postalCode;
        document.deliveryAddress = deliveryAddress;

        // Invoice address.
        const invoiceAddress = {};
        if (!!d.invoiceAddress && !!d.invoiceAddress.countryId) {
            invoiceAddress.country = (
                (await app.collection('kernel.countries').findOne({
                    _id: d.invoiceAddress.countryId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            invoiceAddress.address = d.invoiceAddress.address;
            invoiceAddress.city = d.invoiceAddress.city;
            invoiceAddress.district = d.invoiceAddress.district;
            invoiceAddress.subDistrict = d.invoiceAddress.subDistrict;
            invoiceAddress.street = d.invoiceAddress.street;
            invoiceAddress.doorNumber = d.invoiceAddress.doorNumber;
            invoiceAddress.apartmentNumber = d.invoiceAddress.apartmentNumber;
            invoiceAddress.postalCode = d.invoiceAddress.postalCode;
            document.invoiceAddress = invoiceAddress;
        } else {
            invoiceAddress.country = 'Türkiye';
            invoiceAddress.address = '';
            invoiceAddress.city = '';
            invoiceAddress.district = '';
            invoiceAddress.subDistrict = '';
            invoiceAddress.street = '';
            invoiceAddress.doorNumber = '';
            invoiceAddress.apartmentNumber = '';
            invoiceAddress.postalCode = '';
            document.invoiceAddress = invoiceAddress;
        }

        // Logistic.
        const deliveryPriorityOptions = [
            {value: 'not-urgent', label: 'Not urgent'},
            {value: 'normal', label: 'Normal'},
            {value: 'urgent', label: 'Urgent'},
            {value: 'very-urgent', label: 'Very urgent'}
        ];
        const deliveryPolicyOptions = [
            {value: 'when-one-ready', label: 'When a product is ready'},
            {value: 'when-all-ready', label: 'When all products are ready'}
        ];
        const shippingPaymentTypeOptions = [
            {value: 'freight-prepaid', label: 'Freight prepaid'},
            {value: 'freight-collect', label: 'Freight collect'}
        ];
        if (!!d.deliveryPriority) {
            document.deliveryPriority = t(deliveryPriorityOptions.find(o => o.value === d.deliveryPriority).label);
        }
        if (!!d.deliveryPolicy) {
            document.deliveryPolicy = t(deliveryPolicyOptions.find(o => o.value === d.deliveryPolicy).label);
        }
        if (!!d.deliveryConditionId) {
            document.deliveryCondition = (
                (await app.collection('logistics.delivery-conditions').findOne({
                    _id: d.deliveryConditionId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
        }
        if (!!d.deliveryMethodId) {
            document.deliveryMethod = (
                (await app.collection('logistics.delivery-methods').findOne({
                    _id: d.deliveryMethodId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
        }
        if (!!d.carrierId) {
            document.carrier = (
                (await app.collection('logistics.carriers').findOne({
                    _id: d.carrierId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
        }
        document.deliveryNote = d.deliveryNote || '';
        document.shipmentNote = d.shipmentNote || '';
        document.cargoTrackingCode = d.cargoTrackingCode || '';
        if (!!d.shippingPaymentType) {
            document.shippingPaymentType = t(
                shippingPaymentTypeOptions.find(o => o.value === d.shippingPaymentType).label
            );
        }

        // Company address
        document.companyName = company.name;
        document.companyAddress = company.address;

        // Branch.
        document.branchName = (d.branch || {}).name || '';
        document.branchAddress = ((d.branch || {}).address || {}).address || '';

        // Partner info.
        let partnerName = '';
        if (!!document.partner) {
            partnerName = document.partner.name;

            if (document.partner.isCompany) {
                partnerName = document.partner.legalName;
            }
        }
        document.partnerName = partnerName;

        let sourceName = '';
        let sourceAddress = '';
        let destinationName = '';
        let destinationAddress = document.deliveryAddress.address;
        if (app.setting('system.multiBranch') && !!d.sourceLocation.branchId) {
            const sourceWarehouse = await app.collection('inventory.warehouses').findOne({
                _id: d.sourceLocation.warehouseId,
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            const sourceBranch = await app.collection('kernel.branches').findOne({
                _id: d.sourceLocation.branchId,
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            sourceName = `${company.name} - ${sourceBranch.name}`;
            sourceAddress = ((sourceWarehouse || {}).address || {}).address;
        } else {
            const sourceWarehouse = await app.collection('inventory.warehouses').findOne({
                _id: d.sourceLocation.warehouseId,
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            sourceName = company.name;
            sourceAddress = ((sourceWarehouse || {}).address || {}).address;
        }
        if (!!document.partner) {
            destinationName = partnerName;
        } else if (app.setting('system.multiBranch')) {
            const destinationBranch = await app.collection('kernel.branches').findOne({
                _id: d.destinationLocation.branchId,
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            destinationName = `${company.name} - ${destinationBranch.name}`;
        }
        document.sourceName = sourceName;
        document.sourceAddress = sourceAddress;
        document.destinationName = destinationName;
        document.destinationAddress = destinationAddress;

        // Financial project.
        if (!!d.financialProjectId) {
            const financialProject = await app.collection('kernel.financial-projects').findOne({
                _id: d.financialProjectId,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            document.financialProjectName = financialProject.name;
            document.financialProjectCode = financialProject.code;
        }

        // Items
        const products = await app.collection('inventory.products').find({
            _id: {$in: (d.items || []).map(item => item.productId)},
            $select: ['type', 'isSimple', 'image', 'barcode', 'hsCode', 'unitMeasurements', 'countryOfOriginId','baseUnitId'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const productsMap = {};
        for (const product of products) {
            productsMap[product._id] = product;
        }
        const units = await app.collection('kernel.units').find();
        const unitsMap = {};
        for (const unit of units) {
            unitsMap[unit._id] = unit;
        }
        const lengthUnit = await app.collection('kernel.units').findOne({
            category: 'length',
            symbol: 'm',
            $select: ['symbol']
        });
        const weightUnit = await app.collection('kernel.units').findOne({
            category: 'weight',
            symbol: 'kg',
            $select: ['symbol']
        });
        let totalQty = 0;
        let totalNetWeight = 0;
        let totalGrossWeight = 0;

        const subItemMap = {};
        const locationMap = {};

        const subItems = await app.collection('inventory.transfer-sub-items').find({
            transferId: d._id,
            $select: [
                'itemId',
                'specialUnitId',
                'specialQty',
                'destinationLocationId',
                'sourceLocationId',
                'lotNumber',
                'serialNumber'
            ]
        });
        for (const subItem of subItems) {
            subItemMap[subItem.itemId] = subItem;
        }

        if (!!app.setting('inventory.useBulkEntryInTransfers')) {
            const locations = await app.collection('inventory.locations').find({
                _id: {
                    $in: _.uniq(
                        subItems.map(i => i.sourceLocationId).concat(subItems.map(i => i.destinationLocationId))
                    )
                },
                $select: ['_id', 'shortName', 'path', 'description'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            for (const location of locations) {
                locationMap[location._id] = location;
            }
        }

        const items = [];
        for (const i of d.items || []) {
            const product = productsMap[i.productId];
            const subItem = subItemMap[i.id];
            const item = {
                productId: i.productId
            };

            i.baseQuantity = i.actualQty;

            if (subItem) {
                item.sourceLocationName = locationMap[subItem.sourceLocationId]?.path ?? '';
                item.sourceLocationDescription = locationMap[subItem.sourceLocationId]?.description ?? '';
                item.destinationLocationName = locationMap[subItem.destinationLocationId]?.path ?? '';
                item.destinationLocationDescription = locationMap[subItem.destinationLocationId]?.description ?? '';

                item.lotNumber = subItem.lotNumber || '';
                item.serialNumber = subItem.serialNumber || '';

                if (subItem.lotNumber) {
                    const lotNumber = await app.collection('inventory.lot-numbers').findOne({
                        lotNumber: subItem.lotNumber,
                        $select: ['expirationDate']
                    });
                    item.expirationDate = lotNumber?.expirationDate ? f(lotNumber.expirationDate, 'date') : '';
                } else {
                    item.expirationDate = '';
                }

                const specialUnit = unitsMap[subItem.specialUnitId];

                item.specialQuantity = subItem.specialQty ?? 0;
                item.specialUnitName = t(specialUnit?.name ?? '');
                item.specialUnitSymbol = specialUnit?.symbol ?? '';
            }

            let countryOfOrigin = null;
            if (!!product.countryOfOriginId) {
                countryOfOrigin = await app.collection('kernel.countries').findOne({
                    _id: product.countryOfOriginId,
                    $select: ['name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true,
                    $disableTranslation: true
                });
            }

            item.productCode = i.productCode;
            item.productDefinition = i.productDefinition;
            item.deliveryNote = i.deliveryNote || '';
            item.description = (i.description || '').split('\r\n').join('<br/>').split('\n').join('<br/>');
            item.barcode = i.barcode || product.barcode;
            item.hsCode = product.hsCode || '';
            item.countryOfOrigin = !!product.countryOfOriginId ? (countryOfOrigin || {}).name || '' : '';
            item.quantity = f(i.actualQty, 'unit');
            item.requestedQuantity = f(i.requestedQty, 'unit');
            item.unit = (
                (await app.collection('kernel.units').findOne({
                    _id: i.unitId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            item.unitPrice = f(i.unitPrice, 'unit-price');

            if (!!product.image) {
                item.image = app.absoluteUrl(product.image);
            } else {
                item.image = app.absoluteUrl('static/images/no-image.png');
            }

            const round = n => app.roundNumber(n, 2);
            if (!!lengthUnit && !!weightUnit) {
                const um = product.unitMeasurements.find(um => um.unitId === product.baseUnitId);
                item.height = 0;
                item.heightUnitId = lengthUnit._id;
                item.width = 0;
                item.widthUnitId = lengthUnit._id;
                item.depth = 0;
                item.depthUnitId = lengthUnit._id;
                item.netWeight = 0;
                item.netWeightUnitId = weightUnit._id;
                item.grossWeight = 0;
                item.grossWeightUnitId = weightUnit._id;
                if (!!um) {
                    if (um.height && um.heightUnitId) {
                        item.height = um.height;
                        item.heightUnitId = um.heightUnitId;
                    }
                    if (um.width && um.widthUnitId) {
                        item.width = um.width;
                        item.widthUnitId = um.widthUnitId;
                    }
                    if (um.depth && um.depthUnitId) {
                        item.depth = um.depth;
                        item.depthUnitId = um.depthUnitId;
                    }
                    if (um.netWeight && um.netWeightUnitId) {
                        item.netWeight = um.netWeight;
                        item.netWeightUnitId = um.netWeightUnitId;
                    }
                    if (um.grossWeight && um.grossWeightUnitId) {
                        item.grossWeight = um.grossWeight;
                        item.grossWeightUnitId = um.grossWeightUnitId;
                    }
                }
                const heightUnit = unitsMap[item.heightUnitId];
                if (heightUnit) {
                    if (heightUnit.type === 'smaller') item.height = item.height / heightUnit.ratio;
                    else if (heightUnit.type === 'bigger') item.height = item.height * heightUnit.ratio;
                }
                const widthUnit = unitsMap[item.widthUnitId];
                if (widthUnit) {
                    if (widthUnit.type === 'smaller') item.width = item.width / widthUnit.ratio;
                    else if (widthUnit.type === 'bigger') item.width = item.width * widthUnit.ratio;
                }
                const depthUnit = unitsMap[item.depthUnitId];
                if (depthUnit) {
                    if (depthUnit.type === 'smaller') item.depth = item.depth / depthUnit.ratio;
                    else if (depthUnit.type === 'bigger') item.depth = item.depth * depthUnit.ratio;
                }
                const netWeightUnit = unitsMap[item.netWeightUnitId];
                if (netWeightUnit) {
                    if (netWeightUnit.type === 'smaller') item.netWeight = item.netWeight / netWeightUnit.ratio;
                    else if (netWeightUnit.type === 'bigger') item.netWeight = item.netWeight * netWeightUnit.ratio;
                }
                const grossWeightUnit = unitsMap[item.grossWeightUnitId];
                if (grossWeightUnit) {
                    if (grossWeightUnit.type === 'smaller') item.grossWeight = item.grossWeight / grossWeightUnit.ratio;
                    else if (grossWeightUnit.type === 'bigger')
                        item.grossWeight = item.grossWeight * grossWeightUnit.ratio;
                }
                item.totalNetWeight = round(round(item.netWeight) * round(i.baseQuantity));
                item.totalGrossWeight = round(round(item.grossWeight) * round(i.baseQuantity));
                totalNetWeight += round(round(item.netWeight) * round(i.baseQuantity));
                totalGrossWeight += round(round(item.grossWeight) * round(i.baseQuantity));
            }
            totalQty += i.baseQuantity;

            items.push(item);
        }
        document.items = items;
        document.totalQty = totalQty;
        document.totalNetWeight = totalNetWeight;
        document.totalGrossWeight = totalGrossWeight;

        // Additional information
        if (!_.isEmpty(d.additionalInformationId)) {
            const additionalInformation = await app.collection('kernel.additional-information').findOne({
                _id: d.additionalInformationId,
                $select: ['fields'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            if (additionalInformation) {
                const fields = [];
                for (const key of Object.keys(d.additionalInformation || {})) {
                    const field = (additionalInformation.fields || []).find(f => f.code === key);
                    let value = d.additionalInformation[key];

                    if (typeof value === 'undefined' || value === null) {
                        value = '';
                    } else {
                        if (field.fieldType === 'money') {
                            value = f(value, 'currency');
                        } else if (field.fieldType === 'decimal') {
                            value = f(value, 'amount');
                        } else if (field.fieldType === 'boolean') {
                            value = !!value ? t('Yes') : t('No');
                        } else if (field.fieldType === 'yes-no') {
                            value = value === 'yes' ? t('Yes') : t('No');
                        } else if (field.fieldType === 'date') {
                            value = f(value, 'date');
                        } else if (field.fieldType === 'datetime') {
                            value = f(value, 'datetime');
                        } else if (field.fieldType === 'time') {
                            value = f(value, 'time');
                        }
                    }

                    if (!value) {
                        continue;
                    }

                    fields.push({
                        name: key,
                        label: field.label,
                        value,
                        group: field.group || undefined
                    });
                }
                document.fields = fields;

                const groupedFields = [];
                if (fields.every(f => !!f.group)) {
                    const grouped = _.groupBy(fields, 'group');

                    for (const group of Object.keys(grouped)) {
                        groupedFields.push({
                            group,
                            fields: grouped[group]
                        });
                    }
                }
                if (groupedFields.length > 0) {
                    document.groupedFields = groupedFields;
                }
            }
        }

        // Stocks.
        const operationType = await app.collection('inventory.operation-types').findOne({
            _id: d.operationTypeId,
            $select: ['type', 'warehouseId'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const quantityReport = await app.collection('inventory.quantities').aggregate([
            {
                $match: {
                    productId: {$in: (d.items ?? []).map(item => item.productId)},
                    warehouseId: operationType.warehouseId,
                    'location.type': 'internal'
                }
            },
            {
                $group: {
                    _id: {productId: '$productId', locationId: '$locationId'},
                    location: {$first: '$location'},
                    qty: {$sum: '$qty'},
                    specialQty: {$sum: '$specialQty'},
                    lotNumbers: {$addToSet: '$lotNumber'}
                }
            }
        ]);
        document.items = document.items.map(item => {
            const qrs = quantityReport.filter(r => r._id.productId === item.productId);

            item.stocks = [];
            for (const qr of qrs) {
                const stock = {};

                stock.locationPath = qr.location.path;
                stock.quantity = f(qr.qty ?? 0, 'unit');
                stock.specialQuantity = f(qr.specialQty ?? 0, 'unit');
                stock.lotCount = (qr.lotNumbers ?? []).filter(ln => _.isString(ln)).length;

                item.stocks.push(stock);
            }

            return item;
        });

        return document;
    },
    async sample(app, params) {
        const {locale} = params;
        const t = (text, data = {}) => app.translate(text, locale, data);
        const f = (amount, type) => app.format(amount, type);
        const company = await app.collection('kernel.company').findOne({});
        const statusOptions = [
            {value: 'draft', label: 'Draft', color: 'default'},
            {value: 'waiting', label: 'Waiting', color: 'warning'},
            {value: 'ready', label: 'Ready', color: 'success'},
            {value: 'approved', label: 'Approved', color: 'primary'},
            {value: 'canceled', label: 'Canceled', color: 'danger'}
        ];
        const document = {};

        // General.
        document.locale = locale;
        document.status = t(statusOptions.find(o => o.value === 'draft').label);
        document.code = 'MDOUT00000001';
        document.reference = 'Test ref';
        document.documentType = (await app.collection('sale.document-types').findOne({})).name;
        document.currency = company.currency.name;
        document.currencyRate = 1;
        document.recordDate = f(app.datetime.local().toJSDate(), 'datetime');
        document.issueDate = f(app.datetime.local().toJSDate(), 'date');
        document.scheduledDate = f(app.datetime.local().toJSDate(), 'date');
        document.scheduledTime = f(app.datetime.local().toJSDate(), 'time').split(':').slice(0, 2).join(':');
        document.note = `
Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam nihil placeat praesentium
sed? Accusantium aliquid architecto doloribus fuga illum maxime molestias mollitia nisi,
officiis quidem ratione, recusandae, repudiandae tenetur voluptatum? Lorem ipsum dolor sit
amet, consectetur adipisicing elit. Autem dignissimos doloribus nam omnis similique? Ab,
aperiam aut dicta facilis hic id ipsa, molestiae neque officiis omnis quam quibusdam
repudiandae suscipit!
        `.trim();
        document.partnerOrderReference = 'PO00000001';
        document.documentNo = "DN0000001"

        // Partner.
        const partner = {};
        partner.code = 'M00000001';
        partner.isCompany = true;
        partner.name = 'EnterSoft';
        partner.legalName = 'EnterSoft Inc';
        partner.email = '<EMAIL>';
        partner.language = 'Turkish';
        partner.currency = company.currency.name;
        partner.website = 'https://entersoft.com.tr';
        partner.identity = '';
        partner.tin = '3334445556';
        partner.taxDepartment = 'KARTAL';
        partner.phone = '+90(850) 454 45 45';
        partner.address = 'Cevizli Mahallesi, Tugay Yolu Caddesi, No: 20/A/10, 34846 Maltepe/İstanbul Türkiye';
        partner.country = (await app.collection('kernel.countries').findOne({code: 'TR'})).name;
        partner.city = 'İstanbul';
        partner.district = 'Maltepe';
        partner.subDistrict = 'Cevizli Mahallesi';
        partner.street = 'Tugay Yolu Caddesi';
        partner.doorNumber = '20/A';
        partner.apartmentNumber = '10';
        partner.postalCode = '34846';
        document.partner = partner;

        // Delivery address.
        const deliveryAddress = {};
        deliveryAddress.country = (await app.collection('kernel.countries').findOne({code: 'TR'})).name;
        deliveryAddress.city = 'İstanbul';
        deliveryAddress.district = 'Maltepe';
        deliveryAddress.subDistrict = 'Cevizli Mahallesi';
        deliveryAddress.street = 'Tugay Yolu Caddesi';
        deliveryAddress.doorNumber = '20/A';
        deliveryAddress.apartmentNumber = '10';
        deliveryAddress.postalCode = '34846';
        deliveryAddress.address = 'Cevizli Mahallesi, Tugay Yolu Caddesi, No: 20/A/10, 34846 Maltepe/İstanbul Türkiye';
        document.deliveryAddress = deliveryAddress;

        // Invoice address.
        document.invoiceAddress = deliveryAddress;

        // Partner info.
        document.partnerName = document.partner.name;
        document.partnerAddress = deliveryAddress.address;

        // Company address
        document.companyName = company.name;
        document.companyAddress = company.address.address;

        // Branch.
        document.branchName = 'Test Branch Office';
        document.branchAddress = document.companyAddress;

        document.sourceName = document.companyName;
        document.sourceAddress = company.address.address;
        document.destinationName = document.partnerName;
        document.destinationAddress = deliveryAddress.address;

        // Logistics.
        document.deliveryPriority = 'Normal';
        document.deliveryPolicy = 'When all products are ready';
        document.deliveryCondition = 'Sample';
        document.deliveryMethod = 'Sample';
        document.deliveryNote = 'Test delivery note';
        document.shipmentNote = 'Test shipment note';
        document.carrier = 'DHL';
        document.cargoTrackingCode = 'AAAD333334';
        document.shippingPaymentType = ' Freight prepaid';

        // Financial project.
        document.financialProjectName = 'Test project';
        document.financialProjectCode = 'TP001';

        // Additional information.
        const fields = [
            {name: 'test1', label: 'Test 1', value: 'test 1', group: 'Group 1'},
            {name: 'test2', label: 'Test 2', value: 'test 2', group: 'Group 1'},
            {name: 'test3', label: 'Test 3', value: 'test 3', group: 'Group 1'},
            {name: 'test4', label: 'Test 4', value: 'test 4', group: 'Group 2'},
            {name: 'test5', label: 'Test 5', value: 'test 5', group: 'Group 2'},
            {name: 'test6', label: 'Test 6', value: 'test 6', group: 'Group 2'}
        ];
        document.fields = fields;

        const groupedFields = [];
        if (fields.every(f => !!f.group)) {
            const grouped = _.groupBy(fields, 'group');

            for (const group of Object.keys(grouped)) {
                groupedFields.push({
                    group,
                    fields: grouped[group]
                });
            }
        }
        document.groupedFields = groupedFields;

        // Items.
        document.items = [
            {
                image: app.absoluteUrl('static/images/no-image.png'),
                productCode: 'U00000001',
                productDefinition: 'Test product 1',
                productType: t('Stockable product'),
                barcode: '1200124526541',
                hsCode: '1200124526FFG541',
                deliveryNote: 'Test delivery note',
                countryOfOrigin: 'Turkey',
                description: 'U00000001 - Test product 1',
                quantity: f(3, 'unit'),
                unit: 'EA',
                unitPrice: f(100, 'unit-price'),
                height: 0,
                width: 0,
                depth: 0,
                netWeight: 0,
                grossWeight: 0,
                totalNetWeight: 0,
                totalGrossWeight: 0,
                expirationDate: f(new Date(), 'date'),
                stocks: [
                    {
                        locationPath: 'MD/STOCK',
                        quantity: 10,
                        specialQuantity: 2,
                        lotCount: 1
                    }
                ]
            },
            {
                image: app.absoluteUrl('static/images/no-image.png'),
                productCode: 'U00000002',
                productDefinition: 'Test product 2',
                productType: t('Stockable product'),
                barcode: '1200124526542',
                hsCode: '120012452ADF6541',
                deliveryNote: 'Test delivery note',
                countryOfOrigin: 'Turkey',
                description: 'U00000001 - Test product 2',
                quantity: f(1, 'unit'),
                unit: 'EA',
                unitPrice: f(50, 'unit-price'),
                height: 0,
                width: 0,
                depth: 0,
                netWeight: 0,
                grossWeight: 0,
                totalNetWeight: 0,
                totalGrossWeight: 0,
                expirationDate: f(new Date(), 'date')
            },
            {
                image: app.absoluteUrl('static/images/no-image.png'),
                productCode: 'U00000003',
                productDefinition: 'Test product 3',
                productType: t('Stockable product'),
                barcode: '1200124526543',
                deliveryNote: 'Test delivery note',
                hsCode: '120012452AA6541',
                countryOfOrigin: 'Turkey',
                description: 'U00000001 - Test product 3',
                quantity: f(5, 'unit'),
                unit: 'EA',
                unitPrice: f(200, 'unit-price'),
                height: 0,
                width: 0,
                depth: 0,
                netWeight: 0,
                grossWeight: 0,
                totalNetWeight: 0,
                totalGrossWeight: 0,
                expirationDate: f(new Date(), 'date')
            }
        ];
        document.totalQty = 0;
        document.totalNetWeight = 0;
        document.totalGrossWeight = 0;

        return document;
    },
    content: (app, recordId) =>
        fs.readFile(path.join(app.config('paths.static'), 'templates/inventory/waybill.hbs'), {encoding: 'utf8'})
};
